'use client';

import React, { useMemo, useCallback } from 'react';
import { cn } from '@/utils/cn';
import Icon from '@/components/atoms/Icon';

type TextPart = {
  type: 'text';
  content: string;
};

type BlankPart = {
  type: 'blank';
  index: number;
  correctAnswer: string;
  originalPattern: string;
};

type ContentPart = TextPart | BlankPart;

type FillBlankRendererProps = {
  content: string;
  answer: string[];
  isHtmlContent?: boolean;
  className?: string;
};

export const FillBlankRenderer: React.FC<FillBlankRendererProps> = ({
  content,
  answer,
  isHtmlContent = false,
  className = '',
}) => {
  // No need for user answers or toggle state since we're always showing answers

  // Process content to identify blanks and text parts, handling consecutive blanks
  const processContent = useCallback((text: string): ContentPart[] => {
    if (!text) return [];

    // Enhanced regex to match various blank patterns
    const blankPatterns = /___|___\*|___\.|___,|___!|___\?|\[blank\]|\{blank\}|\(___\)/g;

    // Find all matches with their positions
    const matches = [];
    let match;
    while ((match = blankPatterns.exec(text)) !== null) {
      matches.push({
        pattern: match[0],
        index: match.index,
        length: match[0].length
      });
    }

    // Sort matches by their position in the text
    matches.sort((a, b) => a.index - b.index);

    // Merge consecutive blanks (blanks with only whitespace between them)
    const mergedMatches = [];
    let currentGroup = null;

    for (let i = 0; i < matches.length; i++) {
      const current = matches[i];

      if (!currentGroup) {
        currentGroup = { ...current, answerIndices: [i] };
        continue;
      }

      // Check if there's only whitespace between this blank and the previous one
      const textBetween = text.substring(
        currentGroup.index + currentGroup.length,
        current.index
      );

      if (/^\s*$/.test(textBetween)) {
        // Merge this blank with the current group
        currentGroup.length = (current.index + current.length) - currentGroup.index;
        currentGroup.pattern += textBetween + current.pattern;
        currentGroup.answerIndices.push(i);
      } else {
        // Start a new group
        mergedMatches.push(currentGroup);
        currentGroup = { ...current, answerIndices: [i] };
      }
    }

    // Add the last group if it exists
    if (currentGroup) {
      mergedMatches.push(currentGroup);
    }

    const processed: ContentPart[] = [];
    let lastIndex = 0;

    // Process text segments and merged blanks in order
    for (const match of mergedMatches) {
      // Add text segment before this blank if there is any
      if (match.index > lastIndex) {
        processed.push({
          type: 'text',
          content: text.substring(lastIndex, match.index),
        } as TextPart);
      }

      // Get the answer for this blank (use the first answer index)
      const answerIndex = match.answerIndices[0];

      // Add the blank
      processed.push({
        type: 'blank',
        index: answerIndex,
        correctAnswer: answer[answerIndex] || '',
        originalPattern: match.pattern,
      } as BlankPart);

      // Update lastIndex
      lastIndex = match.index + match.length;
    }

    // Add any remaining text after the last blank
    if (lastIndex < text.length) {
      processed.push({
        type: 'text',
        content: text.substring(lastIndex),
      } as TextPart);
    }

    return processed;
  }, []);

  const contentParts = useMemo(() => processContent(content), [content, processContent]);

  // No need for handleUserAnswerChange since inputs will be read-only

  return (
    <div className={cn('mt-5 space-y-4', className)}>
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold text-gray-800">
          Fill in the blanks:
        </h3>
      </div>

      <div className="bg-white border border-gray-200 rounded-lg p-4">
        <div className="text-lg leading-relaxed">
          {contentParts.map((part: ContentPart, index: number) => (
            <span key={index}>
              {part.type === 'text' ? (
                isHtmlContent ? (
                  <span dangerouslySetInnerHTML={{ __html: part.content || '' }} />
                ) : (
                  <span>{(part as TextPart).content}</span>
                )
              ) : (
                <span className="relative inline-block mx-1">
                  <input
                    type="text"
                    className="inline-block rounded-md border border-primary bg-blue-50 px-3 py-1 text-lg font-medium min-w-24 text-primary"
                    placeholder="____"
                    value={(part as BlankPart).correctAnswer || ''}
                    readOnly
                  />
                </span>
              )}
            </span>
          ))}
        </div>
      </div>

    </div>
  );
};
