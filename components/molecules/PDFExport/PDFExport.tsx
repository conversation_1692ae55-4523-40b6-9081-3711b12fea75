'use client';

import React from 'react';
import { Document, Page, Text, View, StyleSheet, PDFViewer, PDFDownloadLink, Image, Font } from '@react-pdf/renderer';
import { Question } from '@/components/molecules/QuestionListingView/QuestionListingView';
import { ExamHeader, PrintOptions } from '@/components/molecules/PrintModal/types';

// Register fonts if needed
// Font.register({
//   family: 'Roboto',
//   src: '/fonts/Roboto-Regular.ttf',
// });

// Define styles for PDF document
const styles = StyleSheet.create({
  page: {
    padding: 30,
    fontFamily: 'Helvetica',
  },
  header: {
    marginBottom: 20,
    textAlign: 'center',
  },
  schoolName: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  schoolLogo: {
    width: 'auto',
    height: 40,
    marginBottom: 10,
    alignSelf: 'center',
  },
  examTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 5,
    textTransform: 'uppercase',
  },
  subject: {
    fontSize: 14,
    marginBottom: 5,
  },
  examInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 10,
    fontSize: 12,
  },
  studentInfo: {
    marginTop: 20,
    marginBottom: 20,
  },
  studentField: {
    flexDirection: 'row',
    marginBottom: 10,
  },
  fieldLabel: {
    width: 80,
    fontWeight: 'bold',
  },
  fieldValue: {
    flex: 1,
    borderBottom: '1px solid black',
  },
  instructions: {
    marginTop: 10,
    marginBottom: 20,
    padding: 10,
    border: '1px solid black',
  },
  instructionTitle: {
    fontSize: 12,
    fontWeight: 'bold',
    marginBottom: 5,
    textTransform: 'uppercase',
  },
  instructionText: {
    fontSize: 10,
    marginBottom: 3,
  },
  questionSection: {
    marginTop: 20,
  },
  question: {
    marginBottom: 15,
  },
  questionHeader: {
    fontSize: 12,
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 5,
  },
  questionNumber: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  questionMarks: {
    fontSize: 12,
    fontStyle: 'italic',
  },
  questionContent: {
    fontSize: 12,
    marginBottom: 10,
  },
  options: {
    marginLeft: 20,
  },
  option: {
    fontSize: 12,
    marginBottom: 5,
  },
  fillBlank: {
    fontSize: 12,
    borderBottom: '1px solid black',
    minWidth: 100,
    height: 16,
    // react-pdf doesn't support inline-block
    // display: 'inline-block',
  },
  fillBlankContent: {
    fontSize: 12,
    lineHeight: 1.5,
    marginBottom: 10,
    textAlign: 'justify',
  },
  answerSpace: {
    fontSize: 12,
    marginTop: 10,
    height: 50,
    border: '1px solid #cccccc',
  },
});

interface PDFExportProps {
  questions: Question[];
  examHeader: ExamHeader;
  printOptions: PrintOptions;
}

// Helper function to strip HTML tags
const stripHtml = (html: string) => {
  return html.replace(/<[^>]*>?/gm, '');
};

// Helper function to identify and process fill-in-the-blank content
const processFillBlankContent = (content: string, answers: string[] = []) => {
  // First ensure we're working with plain text (in case HTML was not stripped)
  const plainContent = content.replace(/<[^>]*>?/gm, '');

  // Enhanced regex to match various blank patterns
  const blankPatterns = /___|___\*|___\.|___,|___!|___\?|\[blank\]|\{blank\}|\(___\)/g;

  // Find all matches with their positions
  const matches = [];
  let match;
  const contentCopy = String(plainContent);

  while ((match = blankPatterns.exec(contentCopy)) !== null) {
    matches.push({
      pattern: match[0],
      index: match.index,
      length: match[0].length
    });
  }

  // If no blanks found or no answers provided, just replace with standard underscores
  if (matches.length === 0 || !answers || answers.length === 0) {
    return plainContent.replace(blankPatterns, '________');
  }

  // Process the content with blanks
  const processedContent = plainContent;
  let lastIndex = 0;
  let result = '';
  let answerIndex = 0;

  // Replace each blank with appropriate number of underscores based on answer length
  for (const match of matches) {
    // Add text before this blank
    if (match.index > lastIndex) {
      result += processedContent.substring(lastIndex, match.index);
    }

    // Get the answer for this blank
    const answer = answers[answerIndex] || '';

    // Calculate number of underscores based on answer length (minimum 8)
    const underscoreCount = Math.max(8, answer.length * 1.5);
    const underscores = '_'.repeat(underscoreCount);

    // Add the underscores
    result += underscores;

    // Update indices
    lastIndex = match.index + match.length;
    answerIndex++;

    // Break if we've used all answers
    if (answerIndex >= answers.length) break;
  }

  // Add any remaining text
  if (lastIndex < processedContent.length) {
    result += processedContent.substring(lastIndex);
  }

  return result;
};

export const PDFExport: React.FC<PDFExportProps> = ({ questions = [], examHeader, printOptions }) => {

  // Ensure questions is an array
  const safeQuestions = Array.isArray(questions) ? questions : [];

  return (
    <Document>
      <Page
        size={printOptions.paperSize === 'A4' ? 'A4' : 'LETTER'}
        style={styles.page}
        orientation={printOptions.orientation}>
        {/* Header Section */}
        <View style={styles.header}>
          {examHeader.logoUrl && (
            <View>
              <Image
                src={examHeader.logoUrl}
                style={styles.schoolLogo}
              />
            </View>
          )}
          <Text style={styles.schoolName}>{examHeader.schoolName}</Text>
          <Text style={styles.examTitle}>{examHeader.examTitle}</Text>
          <Text style={styles.subject}>{examHeader.subject}</Text>

          <View style={styles.examInfo}>
            <Text>Date: {examHeader.examDate}</Text>
            {examHeader.duration && <Text>Duration: {examHeader.duration}</Text>}
            {examHeader.gradeLevel && <Text>Grade: {examHeader.gradeLevel}</Text>}
          </View>
        </View>

        {/* Student Information */}
        <View style={styles.studentInfo}>
          <View style={styles.studentField}>
            <Text style={styles.fieldLabel}>Name:</Text>
            <Text style={styles.fieldValue}>{examHeader.studentName}</Text>
          </View>
          <View style={styles.studentField}>
            <Text style={styles.fieldLabel}>Class:</Text>
            <Text style={styles.fieldValue}>{examHeader.className}</Text>
          </View>
        </View>

        {/* Instructions */}
        {printOptions.includeInstructions && (
          <View style={styles.instructions}>
            <Text style={styles.instructionTitle}>Instructions to Candidates</Text>
            <Text style={styles.instructionText}>1. This question paper consists of multiple pages.</Text>
            <Text style={styles.instructionText}>2. Answer all questions in the spaces provided.</Text>
            <Text style={styles.instructionText}>3. Follow all instructions carefully.</Text>
            {printOptions.includeMarkAllocation && (
              <Text style={styles.instructionText}>4. The marks for each question are shown in brackets [ ].</Text>
            )}
          </View>
        )}

        {/* Questions */}
        <View style={styles.questionSection}>
          {safeQuestions.length === 0 ? (
            <View style={styles.question}>
              <Text style={{ fontSize: 14, textAlign: 'center', marginTop: 20, color: '#666' }}>
                No questions available. Please add questions to your worksheet.
              </Text>
            </View>
          ) : (
            safeQuestions.map((question, index) => (
              <View key={index} style={styles.question}>
                <View style={styles.questionHeader}>
                  <Text style={styles.questionNumber}>Question {index + 1}</Text>
                  {printOptions.includeMarkAllocation && (
                    <Text style={styles.questionMarks}>[{question.type === 'multiple_choice' || question.type === 'single_choice' ? 1 : 2} marks]</Text>
                  )}
                </View>

                {/* Question Content - Handle fill_blank differently */}
                {question.type === 'fill_blank' ? (
                  <View style={styles.questionContent}>
                    {/* Process the content to replace blanks with appropriate underscores based on answer length */}
                    <Text style={styles.fillBlankContent}>
                      {processFillBlankContent(question.content, question.answer)}
                    </Text>
                  </View>
                ) : (
                  <View style={styles.questionContent}>
                    <Text>{stripHtml(question.content)}</Text>
                  </View>
                )}

                {/* Question Type Specific Rendering */}
                {(question.type === 'multiple_choice' || question.type === 'single_choice') && question.options && (
                  <View style={styles.options}>
                    {question.options.map((option, optionIndex) => (
                      <View key={optionIndex} style={styles.option}>
                        <Text>(__) {stripHtml(option)}</Text>
                      </View>
                    ))}
                  </View>
                )}

                {question.type === 'creative_writing' && (
                  <View style={styles.answerSpace} />
                )}
              </View>
            ))
          )}
        </View>
      </Page>
    </Document>
  );
};

// Export a component that renders the PDF in a viewer
export const PDFExportViewer: React.FC<PDFExportProps> = (props) => {
  return (
    <PDFViewer width="100%" height="600px" style={{ border: 'none' }}>
      <PDFExport {...props} />
    </PDFViewer>
  );
};

// Export a component that provides a download link
export const PDFExportDownloadLink: React.FC<PDFExportProps & { fileName?: string }> = ({ fileName = 'worksheet.pdf', ...props }) => {
  return (
    <PDFDownloadLink document={<PDFExport {...props} />} fileName={fileName}>
      {({ loading }) =>
        loading ? 'Generating PDF...' : 'Download PDF'
      }
    </PDFDownloadLink>
  );
};
