# Components Documentation

## Table of Contents
- [Overview](#overview)
- [Architecture](#architecture)
- [Atoms](#atoms)
- [Molecules](#molecules)
- [Organisms](#organisms)
- [Templates](#templates)
- [Usage Guidelines](#usage-guidelines)
- [Development Guidelines](#development-guidelines)

## Overview

This project follows the **Atomic Design** methodology for organizing React components. The component library is built with:

- **Next.js 15.3.0** with React 19
- **TypeScript** for type safety
- **Tailwind CSS** for styling with **DaisyUI** components
- **Class Variance Authority (CVA)** for component variants
- **React Hook Form** with **Zod** validation
- **Tanstack Table** for data tables
- **Radix UI** for accessible primitives

## Architecture

```
components/
├── atoms/          # Basic building blocks (buttons, inputs, labels)
├── molecules/      # Simple groups of atoms (form fields, table rows)
├── organisms/      # Complex components (forms, tables, navigation)
├── templates/      # Page-level layouts
└── shared/         # Shared utilities and common components
```

## Atoms

### Button
**Location:** `components/atoms/Button/Button.tsx`

A versatile button component with multiple variants and loading states.

**Props:**
- `variant`: 'primary' | 'secondary' | 'accent' | 'ghost' | 'outline' | 'error'
- `isLoading`: boolean
- `href`: string (converts to Link component)
- `iconProps`: IconProps for optional icon rendering

**Usage:**
```tsx
import { Button } from '@/components/atoms/Button/Button';

// Basic usage
<Button variant="primary">Click me</Button>

// With loading state
<Button isLoading={true}>Saving...</Button>

// With link
<Button href="/dashboard">Go to Dashboard</Button>

// With icon
<Button iconProps={{ name: 'plus' }}>Add Item</Button>
```

### Input
**Location:** `components/atoms/Input/Input.tsx`

Styled input component with consistent styling and focus states.

**Usage:**
```tsx
import { Input } from '@/components/atoms/Input/Input';

<Input 
  placeholder="Enter your email" 
  type="email"
  disabled={false}
/>
```

### Icon
**Location:** `components/atoms/Icon/`

Icon system supporting Lucide React icons with consistent sizing.

**Usage:**
```tsx
import { Icon } from '@/components/atoms/Icon';

<Icon name="search" size={16} className="text-gray-500" />
```

### Label
**Location:** `components/atoms/Label/Label.tsx`

Accessible label component for form inputs.

### Table Components
**Location:** `components/atoms/Table/`

Set of table components for building data tables:
- `Table`: Main table wrapper
- `THead`: Table header
- `TBody`: Table body
- `TFoot`: Table footer
- `Tr`: Table row
- `Th`: Table header cell
- `Td`: Table data cell

### Other Atoms
- **Container**: Layout wrapper with responsive padding
- **Dialog**: Modal dialog primitive
- **Form**: Form wrapper component
- **Breadcrumb**: Navigation breadcrumb
- **Tag**: Label/tag component
- **Tooltip**: Tooltip component
- **StatusBadge**: Status indicator badge
- **RoleBadge**: User role badge
- **Steps**: Step indicator component
- **Slider**: Range slider component
- **SvgInline**: SVG icon renderer
- **AnimationStyles**: CSS animation utilities

## Molecules

### CustomTable
**Location:** `components/molecules/CustomTable/`

Advanced data table with sorting, pagination, and row selection.

**Features:**
- Manual/automatic pagination
- Row selection
- Sorting capabilities
- Loading states
- Responsive design

**Usage:**
```tsx
import { CustomTable } from '@/components/molecules/CustomTable';

<CustomTable
  columns={columns}
  tableData={data}
  manualPagination={true}
  pageCount={totalPages}
  pagination={pagination}
  onPaginationChange={setPagination}
/>
```

### FormField
**Location:** `components/molecules/FormField/FormField.tsx`

Form field wrapper combining label, input, and error display.

### Form Items
**Location:** `components/molecules/FormItems/`

React Hook Form integrated components:
- **RHFCheckboxGroup**: Checkbox group with RHF
- **RHFCombobox**: Select/combobox with RHF
- **RHFQuestion**: Question field component
- **RHFQuestionWithNote**: Question with notes
- **RHFRangeSlider**: Range slider with RHF
- **SchoolSelector**: School selection component
- **TopicSelector**: Topic selection component

### User Interface Components
- **UserMenuDropdown**: User profile dropdown menu
- **UserTableBulkActions**: Bulk actions for user table
- **UserTableFilterPanel**: Filter panel for user table
- **UserTableHeader**: Table header with actions
- **UserTableRow**: User table row component
- **UserTableSearchBar**: Search functionality

### School Management Components
- **SchoolTableBulkActions**: Bulk actions for schools
- **SchoolTableFilterPanel**: School filter panel
- **SchoolTableHeader**: School table header

### Worksheet Components
- **QuestionRenderer**: Renders different question types
- **QuestionListingView**: Lists questions
- **WorksheetProgressView**: Shows worksheet progress
- **ManageWorksheet**: Worksheet management interface

### Utility Components
- **AlertMessage**: Alert/notification component
- **ErrorDisplay**: Error state display
- **FileUpload**: File upload component
- **InfoField**: Information display field
- **ListingHeader**: Page listing header
- **LoadingWorksheetScreen**: Loading state for worksheets
- **PDFExport**: PDF export functionality
- **PasswordInput**: Password input with visibility toggle
- **PrintModal**: Print dialog modal
- **ProgressBar**: Progress indicator
- **TablePagination**: Table pagination controls
- **DetailPageHeader**: Header for detail pages
- **CheckboxItem**: Individual checkbox item

## Organisms

### Forms
**Location:** `components/organisms/Forms/`

Complex form components with validation:

#### CreateUserForm
**Location:** `components/organisms/Forms/CreateUserForm/CreateUserForm.tsx`

User creation form with role-based field visibility.

**Features:**
- Zod schema validation
- Role-based conditional fields
- Server action integration
- Loading states and error handling

**Usage:**
```tsx
import { CreateUserForm } from '@/components/organisms/Forms/CreateUserForm/CreateUserForm';

<CreateUserForm />
```

#### Other Forms
- **AssignSchoolManagerForm**: Assign managers to schools
- **CreateBrandForm**: Brand creation form
- **CreateSchoolForm**: School creation form
- **SignInForm**: Authentication form

### Data Tables
- **UsersTable**: Complete user management table
- **SchoolsTable**: School management table

### Management Components
- **ExaminationFormatManager**: Manages examination formats
- **NarrativeStructureManager**: Manages narrative structures
- **MangeWorksheet**: Worksheet management system

### Layout Components
- **Sidebar**: Navigation sidebar
- **Footer**: Page footer

### Modal Components
- **DeleteUserModal**: User deletion confirmation
- **DeleteSchoolModal**: School deletion confirmation
- **SchoolModal**: School creation/edit modal

### Detail Components
- **UserDetailsCard**: User detail display
- **SchoolDetailCard**: School detail display
- **UserForm**: User edit form

## Templates

### Dashboard Template
**Location:** `components/templates/Dashboard/Dashboard.tsx`

Main layout template for dashboard pages.

**Props:**
```tsx
interface PageContainerProps {
  pageContainerType?: 'default' | 'gutterless' | 'contained';
  sidebarItems: { label: string; href: string }[];
  children: React.ReactNode;
  userMenuDropdownProps: UserMenuDropdownProps;
  schoolInfo?: ISchoolResponse | null;
}
```

**Usage:**
```tsx
import DashboardTemplate from '@/components/templates/Dashboard/Dashboard';

<DashboardTemplate
  pageContainerType="default"
  sidebarItems={navigationItems}
  userMenuDropdownProps={userMenuProps}
  schoolInfo={schoolData}
>
  <YourPageContent />
</DashboardTemplate>
```

### Other Templates
- **AuthTemplate**: Authentication page layout
- **DetailTemplate**: Detail page layout
- **ListingTemplate**: List page layout

## Usage Guidelines

### 1. Import Convention
```tsx
// Prefer absolute imports
import { Button } from '@/components/atoms/Button/Button';
import { CustomTable } from '@/components/molecules/CustomTable';
```

### 2. Component Composition
```tsx
// Build complex UI by composing simpler components
const UserManagementPage = () => (
  <DashboardTemplate sidebarItems={items}>
    <ListingHeader title="Users" />
    <UserTableFilterPanel />
    <UsersTable data={users} />
    <TablePagination />
  </DashboardTemplate>
);
```

### 3. Form Components
```tsx
// Use React Hook Form with Zod validation
const MyForm = () => {
  const methods = useForm({
    resolver: zodResolver(schema),
  });

  return (
    <Form {...methods}>
      <FormField name="email" label="Email">
        <Input {...register('email')} />
      </FormField>
    </Form>
  );
};
```

### 4. Table Usage
```tsx
// Define columns for CustomTable
const columns = [
  {
    accessorKey: 'name',
    header: 'Name',
    cell: ({ row }) => row.getValue('name'),
  },
  // ... more columns
];

<CustomTable
  columns={columns}
  tableData={data}
  manualPagination={true}
/>
```

## Development Guidelines

### 1. Component Structure
```tsx
// Component file structure
'use client'; // If client component

import React from 'react';
import { cn } from '@/utils/cn'; // For className merging

interface ComponentProps {
  // Props interface
}

const Component: React.FC<ComponentProps> = ({
  children,
  className,
  ...props
}) => {
  return (
    <div 
      className={cn('base-styles', className)}
      {...props}
    >
      {children}
    </div>
  );
};

export { Component };
```

### 2. Styling Guidelines
- Use Tailwind CSS classes
- Leverage DaisyUI components where appropriate
- Use `cn()` utility for conditional classes
- Follow responsive design principles

### 3. Type Safety
- Define proper TypeScript interfaces
- Use generic types for reusable components
- Leverage React's built-in types (HTMLAttributes, etc.)

### 4. Accessibility
- Use semantic HTML elements
- Include proper ARIA attributes
- Ensure keyboard navigation
- Provide focus indicators

### 5. Testing Considerations
- Components should be testable in isolation
- Use data-testid attributes for testing
- Mock external dependencies
- Test accessibility features

### 6. Performance
- Use React.memo for expensive components
- Implement proper key props for lists
- Lazy load heavy components
- Optimize bundle size

## Best Practices

1. **Keep components focused**: Each component should have a single responsibility
2. **Use composition**: Prefer composition over inheritance
3. **Implement proper error boundaries**: Handle errors gracefully
4. **Document complex components**: Include usage examples and prop descriptions
5. **Follow naming conventions**: Use descriptive, consistent naming
6. **Maintain backward compatibility**: Avoid breaking changes when possible
7. **Use semantic versioning**: Version your component changes appropriately

---

For questions or contributions, please refer to the project's contributing guidelines or reach out to the development team.
